
> @cubent/vscode-webview@ build C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\webview-ui
> tsc -b && vite build

src/components/settings/HistoryManagementSettings.tsx(15,23): error TS2314: Generic type 'SetCachedStateField' requires 1 type argument(s).
src/components/settings/HistoryManagementSettings.tsx(31,26): error TS2322: Type '"clearAllChatHistory"' is not assignable to type '"currentApiConfigName" | "customInstructions" | "condensingApiConfigId" | "autoApprovalEnabled" | "alwaysAllowReadOnly" | "alwaysAllowReadOnlyOutsideWorkspace" | "alwaysAllowWrite" | ... 154 more ... | "getMessageUsageData"'.
src/components/settings/HistoryManagementSettings.tsx(73,7): error TS2322: Type '"number"' is not assignable to type 'TextFieldType | undefined'.
 ELIFECYCLE  Command failed with exit code 1.
